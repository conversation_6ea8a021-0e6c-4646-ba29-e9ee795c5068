"use client";
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Eye } from "lucide-react";
import Link from "next/link";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { PurchaseOrderI } from "@/lib/types";

const ToReviewOrders = ({
  purchaseOrders,
}: {
  purchaseOrders: PurchaseOrderI[];
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState("asc");
  const rowsPerPage = 10;

  const filteredOrders = purchaseOrders.filter(
    (po) =>
      String(po.PurchaseOrderDetails?.company?.companyName || "")
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      String(po.PurchaseOrderDetails?.project?.projectName || "")
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      String(po.referenceNumber || "")
        .toLowerCase()
        .includes(searchTerm.toLowerCase()),
  );

  const totalPages = Math.ceil(filteredOrders.length / rowsPerPage);
  const displayedOrders = filteredOrders.slice(
    (currentPage - 1) * rowsPerPage,
    currentPage * rowsPerPage,
  );

  const handleSort = (column: string) => {
    const order = sortColumn === column && sortOrder === "asc" ? "desc" : "asc";
    setSortColumn(column);
    setSortOrder(order);
  };

  const getValue = (po: PurchaseOrderI, column: string) => {
    switch (column) {
      case "referenceNumber":
        return po.referenceNumber || "";
      case "vendor":
        return po.PurchaseOrderDetails?.company?.companyName || "";
      case "projectName":
        return po.PurchaseOrderDetails?.project?.projectName || "";
      case "totalAmount":
        return po.PurchaseOrderDetails?.totalAmount || 0;
      case "status":
        return po.isDraft
          ? "Draft"
          : po?.PurchaseOrderDetails?.PurchaseOrderPayments?.some(
                (p) => p.status === "rejected",
              )
            ? "Rejected"
            : po?.PurchaseOrderDetails?.PurchaseOrderPayments?.some(
                  (p) => p.status !== "idle" && p.status !== "paid",
                )
              ? "In progress"
              : po.isPaid
                ? "Paid"
                : "In progress";
      default:
        return "";
    }
  };

  const sortedOrders = [...displayedOrders].sort((a, b) => {
    if (!sortColumn) return 0;
    const aValue = getValue(a, sortColumn);
    const bValue = getValue(b, sortColumn);
    if (typeof aValue === "number" && typeof bValue === "number") {
      return sortOrder === "asc" ? aValue - bValue : bValue - aValue;
    }
    return sortOrder === "asc"
      ? String(aValue).localeCompare(String(bValue))
      : String(bValue).localeCompare(String(aValue));
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Purchase Orders Requires Your Attention</CardTitle>
      </CardHeader>
      <CardContent>
        <Input
          placeholder="Search by Reference Number, Vendor, or Project Name..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="mb-4"
        />
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead onClick={() => handleSort("referenceNumber")}>
                #
              </TableHead>
              <TableHead onClick={() => handleSort("vendor")}>Vendor</TableHead>
              <TableHead onClick={() => handleSort("projectName")}>
                Project
              </TableHead>
              <TableHead>Description</TableHead>
              <TableHead>type</TableHead>
              <TableHead onClick={() => handleSort("totalAmount")}>
                Total
              </TableHead>
              <TableHead onClick={() => handleSort("status")}>Status</TableHead>
              <TableHead>Created by</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedOrders.map((po, index) => (
              <TableRow key={po.purchaseOrderId}>
                <TableCell className="font-medium">
                  {po.referenceNumber}
                </TableCell>
                <TableCell className="font-medium">
                  <Link
                    className="text-blue-400 hover:underline"
                    target="_blank"
                    href={`/view/vendor/${po.PurchaseOrderDetails?.company?.companyId}`}
                  >
                    {po.PurchaseOrderDetails?.company?.companyName || "N/A"}
                  </Link>
                </TableCell>
                <TableCell className="font-medium">
                  {po.PurchaseOrderDetails?.project?.projectName || "N/A"}
                </TableCell>
                <TableCell className="font-medium">
                  <HoverCard>
                    <HoverCardTrigger>
                      {po?.PurchaseOrderDetails?.description
                        ? po.PurchaseOrderDetails.description.slice(0, 10) +
                          "..."
                        : "N/A"}
                    </HoverCardTrigger>
                    <HoverCardContent>
                      {po?.PurchaseOrderDetails?.description || ""}
                    </HoverCardContent>
                  </HoverCard>
                </TableCell>
                <TableCell className="font-medium">{po.paymentType}</TableCell>

                <TableCell className="font-medium">
                  {po.PurchaseOrderDetails?.totalAmount.toLocaleString(
                    "en-US",
                    {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    },
                  )}
                  {po.PurchaseOrderDetails?.currency || ""}
                </TableCell>
                <TableCell className="font-medium">
                  {getValue(po, "status")}
                </TableCell>
                <TableCell className="font-medium">
                  {po?.userPrepare?.username}
                </TableCell>
                <TableCell>
                  <Link href={`/purchaseOrder/process/${po.purchaseOrderId}`}>
                    <Eye className="cursor-pointer" />
                  </Link>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
          {!sortedOrders.length && (
            <TableFooter>
              <TableRow>
                <TableCell colSpan={8} className="text-center">
                  No Purchase Orders Need Your Attention
                </TableCell>
              </TableRow>
            </TableFooter>
          )}
        </Table>
        <div className="mt-4 flex items-center justify-between">
          <Button
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span>
            Page {currentPage} of {totalPages}
          </span>
          <Button
            onClick={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ToReviewOrders;
