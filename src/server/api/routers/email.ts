import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";
import nodemailer from "nodemailer";
import { env } from "@/env";

let transporter = nodemailer.createTransport({
  port: +env.EMAIL_PORT,
  host: env.EMAIL_HOST,
  auth: {
    user: env.EMAIL_USER,
    pass: env.EMAIL_PASS,
  },
});
export const emailRouter = createTRPCRouter({
  send: protectedProcedure.query(async ({ ctx }) => {
    transporter.sendMail({
      from: "<EMAIL>",
      to: "<EMAIL>",
      subject: "hello fadi",
      text: "this is the first message from the po system , its working fine :) ",
    });
  }),
});
